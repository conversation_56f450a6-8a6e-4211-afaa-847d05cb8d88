// lib/routes/app_routes.dart
import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import 'package:absensi_tahfidz/presentation/pages/auth/login_page.dart';
import 'package:absensi_tahfidz/presentation/pages/auth/dashboard_page.dart';
import 'package:absensi_tahfidz/presentation/pages/auth/santri_page.dart';
import 'package:absensi_tahfidz/presentation/pages/auth/setoran_page.dart';
import 'package:absensi_tahfidz/presentation/pages/auth/home_shell_page.dart';
import 'package:absensi_tahfidz/presentation/pages/settings/attendance_settings_page.dart';
import 'package:absensi_tahfidz/presentation/pages/settings/general_settings_page.dart';

class AppRoutes {
  static const login = '/login';
  static const root = '/';
  static const dashboard = '/dashboard';
  static const santri = '/santri';
  static const setoran = '/setoran';
  static const profile = '/profile';
  static const settings = '/settings';
  static const generalSettings = '/settings/general';
  static const attendanceSettings = '/settings/attendance';

  static const loginName = 'login';
  static const dashboardName = 'dashboard';
  static const santriName = 'santri';
  static const setoranName = 'setoran';
  static const profileName = 'profile';
  static const settingsName = 'settings';
  static const generalSettingsName = 'generalSettings';
  static const attendanceSettingsName = 'attendanceSettings';

  static final routes = <RouteBase>[
    GoRoute(

      path: login,
      name: loginName,
      builder: (context, state) => const LoginPage(),
    ),
    ShellRoute(
      builder: (context, state, child) => HomeShellPage(child: child),
      routes: [
        GoRoute(
          path: root,
          name: dashboardName,
          builder: (context, state) => const DashboardPage(),
        ),
        GoRoute(
          path: santri,
          name: santriName,
          builder: (context, state) => const SantriPage(),
        ),
        GoRoute(
          path: setoran,
          name: setoranName,
          builder: (context, state) => const SetoranPage(),
        ),
        GoRoute(
          path: profile,
          name: profileName,
          builder: (context, state) => const Center(child: Text('Profil Saya')),
        ),
        GoRoute(
          path: settings,
          name: settingsName,
          // Alihkan ke pengaturan umum secara default
          redirect: (_, __) => generalSettings,
          routes: [
            GoRoute(
              path: 'general',
              name: generalSettingsName,
              builder: (context, state) => const GeneralSettingsPage(),
            ),
            GoRoute(
              path: 'attendance',
              name: attendanceSettingsName,
              builder: (context, state) => const AttendanceSettingsPage(),
            ),
          ],
        ),
      ],
    ),
  ];
}
