import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../data/models/user_model.dart';
import '../../../data/services/auth_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

final authProvider = StateNotifierProvider<AuthController, AsyncValue<UserModel?>>(
  (ref) => AuthController(),
);

class AuthController extends StateNotifier<AsyncValue<UserModel?>> {
  AuthController() : super(const AsyncValue.data(null)) {
    checkLogin(); // 🔥 Panggil saat inisialisasi
  }

  final _storage = const FlutterSecureStorage();

  /// Login logic
  Future<void> login(String email, String password) async {
    state = const AsyncValue.loading();
    try {
      final (token, user) = await AuthService.login(email: email, password: password);
      await _storage.write(key: 'token', value: token);
      state = AsyncValue.data(user);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }

  /// Check current login status (dipanggil saat startup)
  Future<void> checkLogin() async {
    try {
      final token = await _storage.read(key: 'token');
      if (token == null) {
        state = const AsyncValue.data(null);
        return;
      }

      final user = await AuthService.getMe();
      state = AsyncValue.data(user);
    } catch (_) {
      state = const AsyncValue.data(null);
    }
  }

  /// Untuk set manual user (jika ada)
  void setUser(UserModel user) {
    state = AsyncValue.data(user);
  }

  /// Logout
  Future<void> logout() async {
    await AuthService.logout();
    state = const AsyncValue.data(null);
  }
}
