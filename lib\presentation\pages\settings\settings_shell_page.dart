import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:absensi_tahfidz/routes/app_routes.dart';

class SettingsShellPage extends StatelessWidget {
  final Widget child;
  const SettingsShellPage({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final location = GoRouterState.of(context).uri.toString();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Pengaturan'),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        scrolledUnderElevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.dashboard),
        ),
      ),
      body: Row(
        children: [
          // Sidebar navigation dengan gaya Rails
          Container(
            width: 280,
            decoration: BoxDecoration(
              color: colorScheme.surface,
              border: Border(
                right: BorderSide(
                  color: colorScheme.outlineVariant,
                  width: 1,
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24.0),
                  decoration: BoxDecoration(
                    color: colorScheme.primaryContainer,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.settings_rounded,
                        size: 32,
                        color: colorScheme.onPrimaryContainer,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Pengaturan Aplikasi',
                        style: textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onPrimaryContainer,
                        ),
                      ),
                      Text(
                        'Kelola konfigurasi sistem',
                        style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Navigation items
                Expanded(
                  child: ListView(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    children: [
                      _SettingsNavItem(
                        icon: Icons.tune,
                        title: 'Pengaturan Umum',
                        subtitle: 'Konfigurasi dasar aplikasi',
                        isSelected: location.contains('/settings/general'),
                        onTap: () => context.go(AppRoutes.generalSettings),
                      ),
                      _SettingsNavItem(
                        icon: Icons.calendar_today,
                        title: 'Pengaturan Absensi',
                        subtitle: 'Atur jadwal dan metode absensi',
                        isSelected: location.contains('/settings/attendance'),
                        onTap: () => context.go(AppRoutes.attendanceSettings),
                      ),
                      const Divider(height: 1),
                      _SettingsNavItem(
                        icon: Icons.security,
                        title: 'Keamanan',
                        subtitle: 'Pengaturan keamanan sistem',
                        isSelected: false,
                        onTap: () {
                          // TODO: Implement security settings
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Fitur akan segera hadir')),
                          );
                        },
                      ),
                      _SettingsNavItem(
                        icon: Icons.backup,
                        title: 'Backup & Restore',
                        subtitle: 'Kelola data backup',
                        isSelected: false,
                        onTap: () {
                          // TODO: Implement backup settings
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Fitur akan segera hadir')),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                
                // Footer
                Container(
                  padding: const EdgeInsets.all(16.0),
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(
                        color: colorScheme.outlineVariant,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.admin_panel_settings,
                        size: 16,
                        color: colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Admin Panel v1.0.0',
                        style: textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Content area
          Expanded(
            child: child,
          ),
        ],
      ),
    );
  }
}

class _SettingsNavItem extends StatelessWidget {
  const _SettingsNavItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.isSelected,
    required this.onTap,
  });

  final IconData icon;
  final String title;
  final String subtitle;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
      decoration: BoxDecoration(
        color: isSelected ? colorScheme.secondaryContainer : null,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        leading: Container(
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: isSelected 
                ? colorScheme.secondary 
                : colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: Icon(
            icon,
            color: isSelected 
                ? colorScheme.onSecondary 
                : colorScheme.onSurfaceVariant,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: textTheme.titleMedium?.copyWith(
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected 
                ? colorScheme.onSecondaryContainer 
                : colorScheme.onSurface,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: textTheme.bodySmall?.copyWith(
            color: isSelected 
                ? colorScheme.onSecondaryContainer.withValues(alpha: 0.8)
                : colorScheme.onSurfaceVariant,
          ),
        ),
        onTap: onTap,
        splashColor: colorScheme.primary.withValues(alpha: 0.1),
        hoverColor: colorScheme.primary.withValues(alpha: 0.05),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
      ),
    );
  }
}
