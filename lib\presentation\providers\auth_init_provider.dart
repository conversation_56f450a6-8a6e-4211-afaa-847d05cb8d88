import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:absensi_tahfidz/data/services/auth_service.dart';
import 'package:absensi_tahfidz/data/models/user_model.dart';
import 'auth_provider.dart';

final authInitProvider = FutureProvider<UserModel?>((ref) async {
  final token = await AuthService.getToken(); // dari secure storage
  if (token == null) return null;

  try {
    final user = await AuthService.getMe(); // GET /me
    ref.read(authProvider.notifier).setUser(user); // masukkan ke authProvider
    return user;
  } catch (_) {
    return null;
  }
});
