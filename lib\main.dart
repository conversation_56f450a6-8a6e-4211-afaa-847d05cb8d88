import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:absensi_tahfidz/core/router/go_router_refresh_notifier.dart';
import 'package:absensi_tahfidz/core/router/auth_redirector.dart';
import 'package:absensi_tahfidz/presentation/providers/auth_provider.dart';
import 'package:absensi_tahfidz/presentation/providers/auth_init_provider.dart';
import 'package:absensi_tahfidz/routes/app_routes.dart';

void main() {
  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authInit = ref.watch(authInitProvider);
    final authNotifier = ref.read(authProvider.notifier);

    if (authInit.isLoading) {
      return const MaterialApp(
        home: Scaffold(
          body: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    final router = GoRouter(
      initialLocation: AppRoutes.root,
      refreshListenable: GoRouterRefreshNotifier(authNotifier.stream),
      redirect: createAuthRedirect(ref),
      routes: AppRoutes.routes,
    );

    return MaterialApp.router(
      title: 'Absensi Tahfidz',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        colorSchemeSeed: Colors.teal,
      ),
      routerConfig: router,
    );
  }
}
