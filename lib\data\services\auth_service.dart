import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';

class AuthService {
  static const String baseUrl = 'http://localhost:8000/api';
  static const _storage = FlutterSecureStorage();
  static final Dio _dio = Dio(BaseOptions(baseUrl: baseUrl));

  /// Login: POST /login
  static Future<(String token, UserModel user)> login({
    required String email,
    required String password,
  }) async {
    final res = await _dio.post('/login', data: {
      'email': email,
      'password': password,
    });

    final token = res.data['token'] as String;
    final user = UserModel.fromJson(res.data['user']);
    return (token, user);
  }

  /// Me: GET /me
  static Future<UserModel> getMe() async {
    final token = await _storage.read(key: 'token');
    if (token == null) throw Exception('Token tidak tersedia');

    final dio = _authorizedDio(token);
    final res = await dio.get('/me');

    return UserModel.fromJson(res.data);
  }

  static Future<String?> getToken() async {
    return await _storage.read(key: 'token');
  }

  /// Logout
  static Future<void> logout() async {
    await _storage.delete(key: 'token');
  }

  /// Dio instance with Bearer token
  static Dio _authorizedDio(String token) {
    final dio = Dio(BaseOptions(baseUrl: baseUrl));
    dio.options.headers['Authorization'] = 'Bearer $token';
    dio.options.headers['Accept'] = 'application/json';
    return dio;
  }
}
