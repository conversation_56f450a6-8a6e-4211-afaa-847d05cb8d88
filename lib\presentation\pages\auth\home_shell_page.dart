import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:absensi_tahfidz/presentation/widgets/responsive_scaffold.dart';
import 'package:absensi_tahfidz/routes/app_routes.dart';

class HomeShellPage extends StatelessWidget {
  final Widget child;
  const HomeShellPage({super.key, required this.child});

  int _getCurrentIndex(BuildContext context) {
    final location = GoRouterState.of(context).uri.toString();
    if (location.startsWith('/santri')) return 1;
    if (location.startsWith('/setoran')) return 2;
    if (location.startsWith('/settings')) return 3;
    if (location.startsWith('/profile')) return 4;
    return 0;
  }

  void _onItemTapped(BuildContext context, int index) {
    final routes = [
      AppRoutes.dashboardName,
      AppRoutes.santriName,
      AppRoutes.setoranName,
      AppRoutes.settingsName,
    ];
    context.goNamed(routes[index]);
  }

  @override
  Widget build(BuildContext context) {
    final currentIndex = _getCurrentIndex(context);
    return ResponsiveScaffold(
      body: child,
      currentIndex: currentIndex,
      onItemTapped: (i) => _onItemTapped(context, i),
    );
  }
}
