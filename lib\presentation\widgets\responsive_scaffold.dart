import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:absensi_tahfidz/presentation/providers/auth_provider.dart';
import 'package:absensi_tahfidz/routes/app_routes.dart';

// Definisi item menu untuk navigasi
class NavMenuItem {
  final String label;
  final IconData icon;
  final IconData selectedIcon; // Icon untuk status terpilih
  final String route;

  const NavMenuItem({
    required this.label,
    required this.icon,
    required this.selectedIcon,
    required this.route,
  });
}

class ResponsiveScaffold extends ConsumerWidget {
  final Widget body;
  final int currentIndex;
  final ValueChanged<int> onItemTapped;
  final String title; // Judul untuk AppBar/Header (sekarang akan ditampilkan di body)

  // List menu item yang akan digunakan
  static const List<NavMenuItem> _navItems = [
    NavMenuItem(
      label: 'Dashboard',
      icon: Icons.dashboard_outlined,
      selectedIcon: Icons.dashboard_rounded,
      route: AppRoutes.dashboard, // Asumsi ada route untuk dashboard
    ),
    NavMenuItem(
      label: 'Santri',
      icon: Icons.people_outline,
      selectedIcon: Icons.people_rounded,
      route: AppRoutes.santri, // Asumsi ada route untuk santri
    ),
    NavMenuItem(
      label: 'Setoran',
      icon: Icons.check_circle_outline,
      selectedIcon: Icons.check_circle_rounded,
      route: AppRoutes.setoran, // Asumsi ada route untuk setoran
    ),
    // New: Pengaturan
    NavMenuItem(
      label: 'Pengaturan',
      icon: Icons.settings_outlined,
      selectedIcon: Icons.settings_rounded,
      route: AppRoutes.settings, // Route ini akan digunakan untuk shell, bukan navigasi langsung
    ),
    // Tambahkan item 'Profil' khusus untuk navigasi mobile
    NavMenuItem(
      label: 'Profil', // Label untuk menu profil
      icon: Icons.person_outline,
      selectedIcon: Icons.person_rounded,
      route: AppRoutes.profile, // Route untuk profil
    ),
  ];

  const ResponsiveScaffold({
    super.key,
    required this.body,
    required this.currentIndex,
    required this.onItemTapped,
    this.title = 'Absensi Tahfidz', // Judul default
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    final bool isLargeScreen = MediaQuery.of(context).size.width >= 720;

    Future<void> handleLogout() async {
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Text(
            'Konfirmasi Logout',
            style: textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          content: Text(
            'Apakah Anda yakin ingin keluar dari aplikasi?',
            style: textTheme.bodyLarge,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              style: TextButton.styleFrom(
                foregroundColor: colorScheme.primary,
                textStyle: textTheme.labelLarge,
              ),
              child: const Text('Batal'),
            ),
            FilledButton.icon(
              onPressed: () => Navigator.pop(context, true),
              icon: const Icon(Icons.logout),
              label: const Text('Logout'),
              style: FilledButton.styleFrom(
                backgroundColor: colorScheme.error,
                foregroundColor: colorScheme.onError,
                textStyle: textTheme.labelLarge,
              ),
            ),
          ],
        ),
      );

      if (confirmed ?? false) {
        await ref.read(authProvider.notifier).logout();
        if (context.mounted) {
          context.go(AppRoutes.login);
        }
      }
    }

    void showSettingsOptions(BuildContext context) {
      final theme = Theme.of(context);
      final colorScheme = theme.colorScheme;
      final textTheme = theme.textTheme;

      showModalBottomSheet(
        context: context,
        builder: (context) {
          return SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Pengaturan',
                    style: textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
                _BottomSheetActionTile(
                  icon: Icons.tune,
                  title: 'Pengaturan Umum',
                  onTap: () {
                    if (context.mounted) context.go(AppRoutes.generalSettings);
                  },
                ),
                _BottomSheetActionTile(
                  icon: Icons.calendar_today,
                  title: 'Pengaturan Absensi',
                  onTap: () {
                    if (context.mounted) context.go(AppRoutes.attendanceSettings);
                  },
                ),
                const SizedBox(height: 16),
              ],
            ),
          );
        },
      );
    }

    void _showAboutDialog(BuildContext context) {
      showAboutDialog(
        context: context,
        applicationName: 'Absensi Tahfidz',
        applicationVersion: '1.0.0',
        applicationIcon: const Icon(Icons.school, size: 48),
        children: [
          const Text('Aplikasi manajemen absensi untuk santri tahfidz Al-Quran.'),
        ],
      );
    }

    // Drawer untuk layar besar dengan gaya Rails
    Widget _buildSettingsDrawer(BuildContext context) {
      final theme = Theme.of(context);
      final colorScheme = theme.colorScheme;
      final textTheme = theme.textTheme;

      return Drawer(
        backgroundColor: colorScheme.surface,
        elevation: 1,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(16),
            bottomRight: Radius.circular(16),
          ),
        ),
        child: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header drawer dengan gaya Rails
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24.0),
                decoration: BoxDecoration(
                  color: colorScheme.primaryContainer,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.settings_rounded,
                      size: 32,
                      color: colorScheme.onPrimaryContainer,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Pengaturan',
                      style: textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onPrimaryContainer,
                      ),
                    ),
                    Text(
                      'Kelola aplikasi Anda',
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),

              // Menu items dengan gaya Rails
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  children: [
                    _DrawerMenuItem(
                      icon: Icons.tune,
                      title: 'Pengaturan Umum',
                      subtitle: 'Konfigurasi dasar aplikasi',
                      onTap: () {
                        Navigator.pop(context);
                        context.go(AppRoutes.generalSettings);
                      },
                    ),
                    _DrawerMenuItem(
                      icon: Icons.calendar_today,
                      title: 'Pengaturan Absensi',
                      subtitle: 'Atur jadwal dan metode absensi',
                      onTap: () {
                        Navigator.pop(context);
                        context.go(AppRoutes.attendanceSettings);
                      },
                    ),
                    const Divider(height: 1),
                    _DrawerMenuItem(
                      icon: Icons.info_outline,
                      title: 'Tentang Aplikasi',
                      subtitle: 'Versi dan informasi aplikasi',
                      onTap: () {
                        Navigator.pop(context);
                        _showAboutDialog(context);
                      },
                    ),
                  ],
                ),
              ),

              // Footer dengan gaya Rails
              Container(
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(
                      color: colorScheme.outlineVariant,
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.admin_panel_settings,
                      size: 16,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Admin Panel',
                      style: textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }



    void showAccountOptions(BuildContext context) {
      final theme = Theme.of(context);
      final colorScheme = theme.colorScheme;
      final textTheme = theme.textTheme;

      showModalBottomSheet(
        context: context,
        builder: (context) {
          return SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Akun Saya',
                    style: textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
                _BottomSheetActionTile(
                  icon: Icons.person_outline,
                  title: 'Lihat Profil',
                  onTap: () {
                    if (context.mounted) context.go(AppRoutes.profile);
                  },
                ),
                _BottomSheetActionTile(
                  icon: Icons.logout,
                  title: 'Logout',
                  color: colorScheme.error,
                  onTap: handleLogout,
                ),
                const SizedBox(height: 16),
              ],
            ),
          );
        },
      );
    }

    if (isLargeScreen) {
      // Item navigasi untuk Rail, tidak termasuk item 'Pengaturan' dan 'Profil'
      final railNavItems = _navItems.sublist(0, _navItems.length - 2);

      return Scaffold(
        // Drawer untuk pengaturan dengan gaya Rails
        drawer: _buildSettingsDrawer(context),
        body: Row(
          children: [
            // NavigationRail untuk layar besar
            NavigationRail(
              selectedIndex: (currentIndex >= railNavItems.length) ? null : currentIndex,
              onDestinationSelected: onItemTapped,
              labelType: NavigationRailLabelType.all,
              backgroundColor: colorScheme.surface,
              indicatorColor: colorScheme.secondaryContainer,
              indicatorShape:
                  RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              destinations: railNavItems.map((item) {
                return NavigationRailDestination(
                  icon: Icon(item.icon, color: colorScheme.onSurfaceVariant),
                  selectedIcon: Icon(item.selectedIcon, color: colorScheme.onSecondaryContainer),
                  label: Text(item.label, style: textTheme.labelLarge),
                );
              }).toList(),
              trailing: Expanded(
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Tombol Settings dengan gaya Rails
                        Tooltip(
                          message: 'Pengaturan',
                          child: InkWell(
                            onTap: () => Scaffold.of(context).openDrawer(),
                            borderRadius: BorderRadius.circular(24),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: CircleAvatar(
                                radius: 24,
                                backgroundColor: colorScheme.secondaryContainer,
                                child: Icon(
                                  Icons.settings_rounded,
                                  color: colorScheme.onSecondaryContainer,
                                  size: 28,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text('Pengaturan', style: textTheme.labelLarge),
                        const SizedBox(height: 16),
                        // Tombol Account
                        Tooltip(
                          message: 'Pengaturan Akun',
                          child: InkWell(
                            onTap: () => showAccountOptions(context),
                            borderRadius: BorderRadius.circular(24),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: CircleAvatar(
                                radius: 24,
                                backgroundColor: colorScheme.primaryContainer,
                                child: Icon(
                                  Icons.person_rounded,
                                  color: colorScheme.onPrimaryContainer,
                                  size: 28,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text('Akun', style: textTheme.labelLarge),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            VerticalDivider(
              width: 1,
              color: colorScheme.outlineVariant,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: body),
                ],
              ),
            ),
          ],
        ),
      );
    } else {
      return Scaffold(
        // AppBar dihapus sepenuhnya
        body: Column( // Menggunakan Column untuk menempatkan judul di atas body
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Judul halaman untuk mobile (opsional, bisa juga diatur di masing-masing page)
            // Jika Anda ingin judul selalu ada di sini, uncomment ini:
            // Padding(
            //   padding: const EdgeInsets.fromLTRB(16.0, 56.0, 16.0, 8.0), // Padding disesuaikan
            //   child: Text(
            //     title,
            //     style: textTheme.headlineSmall?.copyWith(
            //       fontWeight: FontWeight.bold,
            //       color: colorScheme.onSurface,
            //     ),
            //   ),
            // ),
            Expanded(child: body),
          ],
        ),
        bottomNavigationBar: NavigationBar(
          selectedIndex: currentIndex,
          onDestinationSelected: (index) {
            // Jika item 'Pengaturan' (sebelum Profil)
            if (index == _navItems.length - 2) {
              showSettingsOptions(context);
            } else if (index == _navItems.length - 1) { // Jika item 'Profil'
              showAccountOptions(context);
            } else {
              onItemTapped(index);
            }
          },
          backgroundColor: colorScheme.surface,
          indicatorColor: colorScheme.secondaryContainer,
          elevation: 3,
          destinations: _navItems.map((item) {
            return NavigationDestination(
              icon: Icon(item.icon, color: colorScheme.onSurfaceVariant),
              selectedIcon: Icon(item.selectedIcon, color: colorScheme.onSecondaryContainer),
              label: item.label,
            );
          }).toList(),
        ),
      );
    }
  }
}

/// Widget helper untuk item di dalam ModalBottomSheet
class _BottomSheetActionTile extends StatelessWidget {
  const _BottomSheetActionTile({
    required this.icon,
    required this.title,
    required this.onTap,
    this.color,
  });

  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final effectiveColor = color ?? theme.colorScheme.onSurfaceVariant;

    return ListTile(
      leading: Icon(icon, color: effectiveColor),
      title: Text(title, style: textTheme.bodyLarge?.copyWith(color: color)),
      onTap: () {
        Navigator.pop(context); // Tutup bottom sheet
        onTap(); // Jalankan aksi
      },
      splashColor: effectiveColor.withValues(alpha: 0.1),
      hoverColor: effectiveColor.withValues(alpha: 0.05),
    );
  }
}

/// Widget helper untuk item di dalam Drawer dengan gaya Rails
class _DrawerMenuItem extends StatelessWidget {
  const _DrawerMenuItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8.0),
      leading: Container(
        padding: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          color: colorScheme.secondaryContainer,
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Icon(
          icon,
          color: colorScheme.onSecondaryContainer,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
          color: colorScheme.onSurface,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: textTheme.bodySmall?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),
      onTap: onTap,
      splashColor: colorScheme.primary.withValues(alpha: 0.1),
      hoverColor: colorScheme.primary.withValues(alpha: 0.05),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
    );
  }
}