import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:absensi_tahfidz/presentation/providers/auth_provider.dart';
import 'package:absensi_tahfidz/routes/app_routes.dart';

// Definisi item menu untuk navigasi
class NavMenuItem {
  final String label;
  final IconData icon;
  final IconData selectedIcon; // Icon untuk status terpilih
  final String route;

  const NavMenuItem({
    required this.label,
    required this.icon,
    required this.selectedIcon,
    required this.route,
  });
}

class ResponsiveScaffold extends ConsumerWidget {
  final Widget body;
  final int currentIndex;
  final ValueChanged<int> onItemTapped;
  final String title; // Judul untuk AppBar/Header (sekarang akan ditampilkan di body)

  static final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // List menu item yang akan digunakan
  static const List<NavMenuItem> _navItems = [
    NavMenuItem(
      label: 'Dashboard',
      icon: Icons.dashboard_outlined,
      selectedIcon: Icons.dashboard_rounded,
      route: AppRoutes.dashboard, // Asumsi ada route untuk dashboard
    ),
    NavMenuItem(
      label: 'Santri',
      icon: Icons.people_outline,
      selectedIcon: Icons.people_rounded,
      route: AppRoutes.santri, // Asumsi ada route untuk santri
    ),
    NavMenuItem(
      label: 'Setoran',
      icon: Icons.check_circle_outline,
      selectedIcon: Icons.check_circle_rounded,
      route: AppRoutes.setoran, // Asumsi ada route untuk setoran
    ),
    // New: Pengaturan
    NavMenuItem(
      label: 'Pengaturan',
      icon: Icons.settings_outlined,
      selectedIcon: Icons.settings_rounded,
      route: AppRoutes.settings, // Route ini akan digunakan untuk shell, bukan navigasi langsung
    ),
    // Tambahkan item 'Profil' khusus untuk navigasi mobile
    NavMenuItem(
      label: 'Profil', // Label untuk menu profil
      icon: Icons.person_outline,
      selectedIcon: Icons.person_rounded,
      route: AppRoutes.profile, // Route untuk profil
    ),
  ];

  const ResponsiveScaffold({
    super.key,
    required this.body,
    required this.currentIndex,
    required this.onItemTapped,
    this.title = 'Absensi Tahfidz', // Judul default
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    final bool isLargeScreen = MediaQuery.of(context).size.width >= 720;

    Future<void> handleLogout() async {
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Text(
            'Konfirmasi Logout',
            style: textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          content: Text(
            'Apakah Anda yakin ingin keluar dari aplikasi?',
            style: textTheme.bodyLarge,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              style: TextButton.styleFrom(
                foregroundColor: colorScheme.primary,
                textStyle: textTheme.labelLarge,
              ),
              child: const Text('Batal'),
            ),
            FilledButton.icon(
              onPressed: () => Navigator.pop(context, true),
              icon: const Icon(Icons.logout),
              label: const Text('Logout'),
              style: FilledButton.styleFrom(
                backgroundColor: colorScheme.error,
                foregroundColor: colorScheme.onError,
                textStyle: textTheme.labelLarge,
              ),
            ),
          ],
        ),
      );

      if (confirmed ?? false) {
        await ref.read(authProvider.notifier).logout();
        if (context.mounted) {
          context.go(AppRoutes.login);
        }
      }
    }

    void showSettingsOptions(BuildContext context) {
      final theme = Theme.of(context);
      final colorScheme = theme.colorScheme;
      final textTheme = theme.textTheme;

      showModalBottomSheet(
        context: context,
        builder: (context) {
          return SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Pengaturan',
                    style: textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
                _BottomSheetActionTile(
                  icon: Icons.tune,
                  title: 'Pengaturan Umum',
                  onTap: () {
                    if (context.mounted) context.go(AppRoutes.generalSettings);
                  },
                ),
                _BottomSheetActionTile(
                  icon: Icons.calendar_today,
                  title: 'Pengaturan Absensi',
                  onTap: () {
                    if (context.mounted) context.go(AppRoutes.attendanceSettings);
                  },
                ),
                const SizedBox(height: 16),
              ],
            ),
          );
        },
      );
    }

    void _showAboutDialog(BuildContext context) {
      showAboutDialog(
        context: context,
        applicationName: 'Absensi Tahfidz',
        applicationVersion: '1.0.0',
        applicationIcon: const Icon(Icons.school, size: 48),
        children: [
          const Text('Aplikasi manajemen absensi untuk santri tahfidz Al-Quran.'),
        ],
      );
    }

    // Drawer untuk layar besar dengan gaya Rails
    Widget _buildSettingsDrawer(BuildContext context) {
      final theme = Theme.of(context);
      final colorScheme = theme.colorScheme;
      final textTheme = theme.textTheme;

      return Drawer(
        backgroundColor: colorScheme.surface,
        elevation: 2,
        width: 320, // Lebar drawer yang lebih besar untuk konten yang lebih kaya
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(28), // Sesuai dengan M3 guidelines
            bottomRight: Radius.circular(28),
          ),
        ),
        child: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header drawer dengan gaya Rails yang konsisten dengan NavigationRail
              Container(
                width: double.infinity,
                padding: const EdgeInsets.fromLTRB(24.0, 32.0, 24.0, 24.0),
                decoration: BoxDecoration(
                  color: colorScheme.primaryContainer,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(28),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Icon dan close button
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12.0),
                          decoration: BoxDecoration(
                            color: colorScheme.primary,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Icon(
                            Icons.settings_rounded,
                            size: 28,
                            color: colorScheme.onPrimary,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: Icon(
                            Icons.close,
                            color: colorScheme.onPrimaryContainer,
                          ),
                          style: IconButton.styleFrom(
                            backgroundColor: colorScheme.onPrimaryContainer.withValues(alpha: 0.1),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Pengaturan Sistem',
                      style: textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onPrimaryContainer,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Kelola konfigurasi dan preferensi aplikasi',
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),

              // Menu items dengan gaya Rails
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  children: [
                    // Section: Konfigurasi
                    Padding(
                      padding: const EdgeInsets.fromLTRB(24.0, 8.0, 24.0, 8.0),
                      child: Text(
                        'KONFIGURASI',
                        style: textTheme.labelSmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                    _DrawerMenuItem(
                      icon: Icons.tune,
                      title: 'Pengaturan Umum',
                      subtitle: 'Konfigurasi dasar aplikasi',
                      onTap: () {
                        Navigator.pop(context);
                        context.go(AppRoutes.generalSettings);
                      },
                    ),
                    _DrawerMenuItem(
                      icon: Icons.calendar_today,
                      title: 'Pengaturan Absensi',
                      subtitle: 'Atur jadwal dan metode absensi',
                      onTap: () {
                        Navigator.pop(context);
                        context.go(AppRoutes.attendanceSettings);
                      },
                    ),

                    const SizedBox(height: 16),

                    // Section: Sistem
                    Padding(
                      padding: const EdgeInsets.fromLTRB(24.0, 8.0, 24.0, 8.0),
                      child: Text(
                        'SISTEM',
                        style: textTheme.labelSmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                    _DrawerMenuItem(
                      icon: Icons.security,
                      title: 'Keamanan',
                      subtitle: 'Pengaturan keamanan sistem',
                      onTap: () {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Fitur akan segera hadir')),
                        );
                      },
                    ),
                    _DrawerMenuItem(
                      icon: Icons.backup,
                      title: 'Backup & Restore',
                      subtitle: 'Kelola data backup',
                      onTap: () {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Fitur akan segera hadir')),
                        );
                      },
                    ),

                    const SizedBox(height: 16),
                    Container(
                      height: 1,
                      margin: const EdgeInsets.symmetric(horizontal: 24.0),
                      color: colorScheme.outlineVariant,
                    ),
                    const SizedBox(height: 16),

                    // Section: Informasi
                    Padding(
                      padding: const EdgeInsets.fromLTRB(24.0, 8.0, 24.0, 8.0),
                      child: Text(
                        'INFORMASI',
                        style: textTheme.labelSmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                    _DrawerMenuItem(
                      icon: Icons.info_outline,
                      title: 'Tentang Aplikasi',
                      subtitle: 'Versi dan informasi aplikasi',
                      onTap: () {
                        Navigator.pop(context);
                        _showAboutDialog(context);
                      },
                    ),
                  ],
                ),
              ),

              // Footer dengan gaya Rails dan informasi aplikasi
              Container(
                padding: const EdgeInsets.all(24.0),
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainerHighest,
                  border: Border(
                    top: BorderSide(
                      color: colorScheme.outlineVariant,
                      width: 1,
                    ),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6.0),
                          decoration: BoxDecoration(
                            color: colorScheme.tertiaryContainer,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.admin_panel_settings,
                            size: 16,
                            color: colorScheme.onTertiaryContainer,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Admin Panel',
                              style: textTheme.labelMedium?.copyWith(
                                color: colorScheme.onSurface,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              'v1.0.0 • Build 2024.12',
                              style: textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Sistem manajemen absensi tahfidz Al-Quran dengan teknologi modern dan antarmuka yang intuitif.',
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                        height: 1.5,
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }



    void showAccountOptions(BuildContext context) {
      final theme = Theme.of(context);
      final colorScheme = theme.colorScheme;
      final textTheme = theme.textTheme;

      showModalBottomSheet(
        context: context,
        builder: (context) {
          return SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Akun Saya',
                    style: textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
                _BottomSheetActionTile(
                  icon: Icons.person_outline,
                  title: 'Lihat Profil',
                  onTap: () {
                    if (context.mounted) context.go(AppRoutes.profile);
                  },
                ),
                _BottomSheetActionTile(
                  icon: Icons.logout,
                  title: 'Logout',
                  color: colorScheme.error,
                  onTap: handleLogout,
                ),
                const SizedBox(height: 16),
              ],
            ),
          );
        },
      );
    }

    if (isLargeScreen) {
      // Item navigasi untuk Rail, tidak termasuk item 'Pengaturan' dan 'Profil'
      final railNavItems = _navItems.sublist(0, _navItems.length - 2);

      return Scaffold(
        key: _scaffoldKey,
        // Drawer untuk pengaturan dengan gaya Rails
        drawer: _buildSettingsDrawer(context),
        body: Row(
          children: [
            // NavigationRail untuk layar besar - Expanded sesuai M3 Guidelines
            NavigationRail(
              selectedIndex: (currentIndex >= railNavItems.length) ? null : currentIndex,
              onDestinationSelected: onItemTapped,
              extended: true, // Membuat NavigationRail expanded
              minExtendedWidth: 256, // Lebar minimum sesuai M3 guidelines (256dp)
              backgroundColor: colorScheme.surface,
              indicatorColor: colorScheme.secondaryContainer,
              indicatorShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(28), // Full rounded sesuai M3
              ),
              useIndicator: true,
              // Header untuk NavigationRail sesuai M3 guidelines
              leading: Padding(
                padding: const EdgeInsets.fromLTRB(16.0, 24.0, 16.0, 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // App icon dan nama
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8.0),
                          decoration: BoxDecoration(
                            color: colorScheme.primaryContainer,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.school_rounded,
                            color: colorScheme.onPrimaryContainer,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Flexible(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Absensi Tahfidz',
                                style: textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: colorScheme.onSurface,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                'Dashboard Admin',
                                style: textTheme.bodySmall?.copyWith(
                                  color: colorScheme.onSurfaceVariant,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // Divider
                    Container(
                      height: 1,
                      color: colorScheme.outlineVariant,
                    ),
                  ],
                ),
              ),
              destinations: railNavItems.map((item) {
                return NavigationRailDestination(
                  icon: Icon(item.icon, color: colorScheme.onSurfaceVariant),
                  selectedIcon: Icon(item.selectedIcon, color: colorScheme.onSecondaryContainer),
                  label: Text(
                    item.label,
                    style: textTheme.labelLarge?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
              trailing: Expanded(
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 24.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Divider sesuai M3 guidelines
                        Container(
                          width: double.infinity,
                          height: 1,
                          margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                          color: colorScheme.outlineVariant,
                        ),
                        const SizedBox(height: 16),

                        // Tombol Settings dengan gaya Rails - Full width
                        Container(
                          width: double.infinity,
                          margin: const EdgeInsets.symmetric(horizontal: 12.0),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: () => _scaffoldKey.currentState?.openDrawer(),
                              borderRadius: BorderRadius.circular(28),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                                child: Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(8.0),
                                      decoration: BoxDecoration(
                                        color: colorScheme.secondaryContainer,
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Icon(
                                        Icons.settings_rounded,
                                        color: colorScheme.onSecondaryContainer,
                                        size: 24,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        'Pengaturan',
                                        style: textTheme.labelLarge?.copyWith(
                                          fontWeight: FontWeight.w500,
                                          color: colorScheme.onSurface,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 8),

                        // Tombol Account - Full width
                        Container(
                          width: double.infinity,
                          margin: const EdgeInsets.symmetric(horizontal: 12.0),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: () => showAccountOptions(context),
                              borderRadius: BorderRadius.circular(28),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                                child: Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(8.0),
                                      decoration: BoxDecoration(
                                        color: colorScheme.primaryContainer,
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Icon(
                                        Icons.person_rounded,
                                        color: colorScheme.onPrimaryContainer,
                                        size: 24,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        'Akun',
                                        style: textTheme.labelLarge?.copyWith(
                                          fontWeight: FontWeight.w500,
                                          color: colorScheme.onSurface,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            VerticalDivider(
              width: 1,
              color: colorScheme.outlineVariant,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: body),
                ],
              ),
            ),
          ],
        ),
      );
    } else {
      return Scaffold(
        // AppBar dihapus sepenuhnya
        body: Column( // Menggunakan Column untuk menempatkan judul di atas body
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Judul halaman untuk mobile (opsional, bisa juga diatur di masing-masing page)
            // Jika Anda ingin judul selalu ada di sini, uncomment ini:
            // Padding(
            //   padding: const EdgeInsets.fromLTRB(16.0, 56.0, 16.0, 8.0), // Padding disesuaikan
            //   child: Text(
            //     title,
            //     style: textTheme.headlineSmall?.copyWith(
            //       fontWeight: FontWeight.bold,
            //       color: colorScheme.onSurface,
            //     ),
            //   ),
            // ),
            Expanded(child: body),
          ],
        ),
        bottomNavigationBar: NavigationBar(
          selectedIndex: currentIndex,
          onDestinationSelected: (index) {
            // Jika item 'Pengaturan' (sebelum Profil)
            if (index == _navItems.length - 2) {
              showSettingsOptions(context);
            } else if (index == _navItems.length - 1) { // Jika item 'Profil'
              showAccountOptions(context);
            } else {
              onItemTapped(index);
            }
          },
          backgroundColor: colorScheme.surface,
          indicatorColor: colorScheme.secondaryContainer,
          elevation: 3,
          destinations: _navItems.map((item) {
            return NavigationDestination(
              icon: Icon(item.icon, color: colorScheme.onSurfaceVariant),
              selectedIcon: Icon(item.selectedIcon, color: colorScheme.onSecondaryContainer),
              label: item.label,
            );
          }).toList(),
        ),
      );
    }
  }
}

/// Widget helper untuk item di dalam ModalBottomSheet
class _BottomSheetActionTile extends StatelessWidget {
  const _BottomSheetActionTile({
    required this.icon,
    required this.title,
    required this.onTap,
    this.color,
  });

  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final effectiveColor = color ?? theme.colorScheme.onSurfaceVariant;

    return ListTile(
      leading: Icon(icon, color: effectiveColor),
      title: Text(title, style: textTheme.bodyLarge?.copyWith(color: color)),
      onTap: () {
        Navigator.pop(context); // Tutup bottom sheet
        onTap(); // Jalankan aksi
      },
      splashColor: effectiveColor.withValues(alpha: 0.1),
      hoverColor: effectiveColor.withValues(alpha: 0.05),
    );
  }
}

/// Widget helper untuk item di dalam Drawer dengan gaya Rails
class _DrawerMenuItem extends StatelessWidget {
  const _DrawerMenuItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 2.0),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16.0),
          splashColor: colorScheme.primary.withValues(alpha: 0.1),
          hoverColor: colorScheme.primary.withValues(alpha: 0.05),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10.0),
                  decoration: BoxDecoration(
                    color: colorScheme.secondaryContainer,
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Icon(
                    icon,
                    color: colorScheme.onSecondaryContainer,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                          height: 1.3,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.chevron_right,
                  color: colorScheme.onSurfaceVariant,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}