// lib/core/router/auth_redirector.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:absensi_tahfidz/presentation/providers/auth_provider.dart';
import 'package:absensi_tahfidz/routes/app_routes.dart';

/// Creates an auth redirect function for GoRouter
///
/// This function can be used as the redirect parameter in GoRouter constructor
String? Function(BuildContext, GoRouterState) createAuthRedirect(WidgetRef ref) {
  return (BuildContext context, GoRouterState state) {
    final isLoggedIn = ref.read(authProvider).value != null;
    final goingToLogin = state.matchedLocation == AppRoutes.login;

    if (!isLoggedIn && !goingToLogin) return AppRoutes.login;
    if (isLoggedIn && goingToLogin) return AppRoutes.root;
    return null;
  };
}
