import 'package:flutter/material.dart';

class AttendanceSettingsPage extends StatelessWidget {
  const AttendanceSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Pengaturan Absensi',
              style: textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Atur jadwal dan metode absensi santri',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 32),

            // Settings content
            Expanded(
              child: <PERSON><PERSON><PERSON><PERSON>(
                children: [
                  _SettingsCard(
                    title: '<PERSON><PERSON><PERSON> Absensi',
                    children: [
                      _SettingsItem(
                        icon: Icons.access_time,
                        title: '<PERSON><PERSON><PERSON>',
                        subtitle: '07:00 WIB',
                        trailing: const Icon(Icons.chevron_right),
                        onTap: () {},
                      ),
                      _SettingsItem(
                        icon: Icons.access_time_filled,
                        title: 'Waktu Selesai',
                        subtitle: '17:00 WIB',
                        trailing: const Icon(Icons.chevron_right),
                        onTap: () {},
                      ),
                      _SettingsItem(
                        icon: Icons.calendar_today,
                        title: 'Hari Aktif',
                        subtitle: 'Senin - Jumat',
                        trailing: const Icon(Icons.chevron_right),
                        onTap: () {},
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _SettingsCard(
                    title: 'Metode Absensi',
                    children: [
                      _SettingsItem(
                        icon: Icons.qr_code,
                        title: 'QR Code',
                        subtitle: 'Aktif',
                        trailing: Switch(
                          value: true,
                          onChanged: (value) {},
                        ),
                        onTap: () {},
                      ),
                      _SettingsItem(
                        icon: Icons.fingerprint,
                        title: 'Sidik Jari',
                        subtitle: 'Tidak Aktif',
                        trailing: Switch(
                          value: false,
                          onChanged: (value) {},
                        ),
                        onTap: () {},
                      ),
                      _SettingsItem(
                        icon: Icons.location_on,
                        title: 'Lokasi GPS',
                        subtitle: 'Aktif',
                        trailing: Switch(
                          value: true,
                          onChanged: (value) {},
                        ),
                        onTap: () {},
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _SettingsCard(
                    title: 'Toleransi',
                    children: [
                      _SettingsItem(
                        icon: Icons.timer,
                        title: 'Keterlambatan',
                        subtitle: '15 menit',
                        trailing: const Icon(Icons.chevron_right),
                        onTap: () {},
                      ),
                      _SettingsItem(
                        icon: Icons.location_searching,
                        title: 'Radius Lokasi',
                        subtitle: '100 meter',
                        trailing: const Icon(Icons.chevron_right),
                        onTap: () {},
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _SettingsCard extends StatelessWidget {
  const _SettingsCard({
    required this.title,
    required this.children,
  });

  final String title;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return Card(
      elevation: 0,
      color: colorScheme.surfaceContainerLow,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: colorScheme.outlineVariant,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Text(
              title,
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface,
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }
}

class _SettingsItem extends StatelessWidget {
  const _SettingsItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    this.trailing,
    this.onTap,
  });

  final IconData icon;
  final String title;
  final String subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return ListTile(
      leading: Icon(
        icon,
        color: colorScheme.onSurfaceVariant,
      ),
      title: Text(
        title,
        style: textTheme.bodyLarge?.copyWith(
          color: colorScheme.onSurface,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: textTheme.bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),
      trailing: trailing,
      onTap: onTap,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }
}